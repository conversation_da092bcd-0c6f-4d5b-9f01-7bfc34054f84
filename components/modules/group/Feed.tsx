import { useEffect, useState, useCallback, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  FlatList,
  TextInput,
  Modal,
  Platform,
  Share,
  Alert,
  Clipboard,
} from "react-native";
import { type EnrichedActivity } from "getstream";
import {
  Heart,
  MessageCircle,
  Pin,
  Share2,
  Send,
  X,
  Camera,
  ImageIcon,
  Plus,
} from "lucide-react-native";
import { Image } from "expo-image";
import { router, useFocusEffect } from "expo-router";
import * as ImagePicker from "expo-image-picker";

import { useAppContext } from "@/context/app";
import type { FeedModule } from "@/lib/api/types";
import {
  useFeedActivities,
  useFeedLike,
  useFeedComment,
  useFeedRealtime,
  useFeedPost,
  useGetUploadUrl,
  useProfile,
} from "@/lib/api/queries";
import { canUserPostToFeed } from "@/lib/utils/feed";

interface Props {
  module: FeedModule;
}

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  isPinned?: boolean;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  own_reactions?: {
    like?: any[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

export const Feed = ({ module }: Props) => {
  const { streamClient } = useAppContext();
  const [commentModalVisible, setCommentModalVisible] = useState(false);
  const [selectedActivity, setSelectedActivity] =
    useState<EnrichedActivityWithText | null>(null);
  const [commentText, setCommentText] = useState("");
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  
  // Post creation states
  const [createPostModalVisible, setCreatePostModalVisible] = useState(false);
  const [postText, setPostText] = useState("");
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageAsset, setSelectedImageAsset] = useState<ImagePicker.ImagePickerAsset | null>(null);

  // Use the new hooks from queries
  const {
    data,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    isRefetching,
  } = useFeedActivities(module.config.feedGroup, module.config.feedId);

  const likeMutation = useFeedLike(
    module.config.feedGroup,
    module.config.feedId
  );
  const commentMutation = useFeedComment(
    module.config.feedGroup,
    module.config.feedId
  );
  const postMutation = useFeedPost(
    module.config.feedGroup,
    module.config.feedId
  );
  const getUploadUrl = useGetUploadUrl();
  const { data: profileData } = useProfile();

  // Set up real-time updates
  useFeedRealtime(module.config.feedGroup, module.config.feedId);

  // Flatten activities from all pages
  const activities = data?.pages.flatMap((page) => page.results) || [];

  console.log("Feed module config:", module.config);
  console.log("Rendering activities:", activities.length);

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 2000);
  };

  const handleLike = async (activityId: string) => {
    try {
      const activity = activities.find((a) => a.id === activityId);
      const isLiked =
        activity?.own_reactions?.like && activity.own_reactions.like.length > 0;
      const reactionId = isLiked
        ? activity.own_reactions.like[0].id
        : undefined;

      await likeMutation.mutateAsync({
        activityId,
        isLiked,
        reactionId,
      });
    } catch (error) {
      console.error("Error toggling like:", error);
    }
  };

  const handleComment = (activity: EnrichedActivityWithText) => {
    setSelectedActivity(activity);
    setCommentModalVisible(true);
  };

  const handleSubmitComment = async () => {
    if (!selectedActivity || !commentText.trim()) return;

    try {
      await commentMutation.mutateAsync({
        activityId: selectedActivity.id,
        text: commentText.trim(),
      });
      setCommentText("");
      setCommentModalVisible(false);
      setSelectedActivity(null);
      showToastMessage("Comment posted!");
    } catch (error) {
      console.error("Error posting comment:", error);
      showToastMessage("Failed to post comment");
    }
  };

  const handleShare = async (activity: EnrichedActivityWithText) => {
    const url = `${process.env.EXPO_PUBLIC_APP_BASE_URL}/post/${activity.id}`;
    try {
      if (Platform.OS === "android") {
        await Share.share({
          message: `Check out this post: ${url}`,
        });
      } else {
        await Share.share({
          message: "Check out this post!",
          url,
        });
      }
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await Clipboard.setStringAsync(text);
      showToastMessage("Link copied to clipboard!");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
    }
  };

  // Check if user has permission to post
  const userRole = profileData?.data?.role;
  const userOrgId = profileData?.data?.organizationId;
  const canPost = canUserPostToFeed(userRole, userOrgId, module.config.feedId);

  // Helper function to check if a file is an image
  const isImageFile = (asset: ImagePicker.ImagePickerAsset): boolean => {
    return asset.type === "image";
  };

  // Image picker functions
  const openImagePicker = async (source: "camera" | "library") => {
    try {
      let result: ImagePicker.ImagePickerResult;

      if (source === "camera") {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Camera permission is required to take photos"
          );
          return;
        }

        result = await ImagePicker.launchCameraAsync({
          mediaTypes: "images",
          allowsEditing: true,
          quality: 0.8,
        });
      } else {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Photo library permission is required to select photos"
          );
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: "images",
          allowsEditing: true,
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage(asset.uri);
        setSelectedImageAsset(asset);
      }
    } catch (error) {
      console.error("Error opening image picker:", error);
      Alert.alert("Error", "Failed to open image picker");
    }
  };

  const handleImagePicker = () => {
    Alert.alert(
      "Select Image",
      "Choose how you'd like to add an image",
      [
        { text: "Camera", onPress: () => openImagePicker("camera") },
        { text: "Photo Library", onPress: () => openImagePicker("library") },
        { text: "Cancel", style: "cancel" },
      ]
    );
  };

  const removeSelectedImage = () => {
    setSelectedImage(null);
    setSelectedImageAsset(null);
  };

  const handleCreatePost = async () => {
    if (!postText.trim()) {
      Alert.alert("Error", "Please enter some text for your post");
      return;
    }

    try {
      let attachment = undefined;

      if (selectedImageAsset) {
        // Get content type from the asset
        const getContentType = (uri: string): string => {
          const extension = uri.split(".").pop()?.toLowerCase();
          switch (extension) {
            case "jpg":
            case "jpeg":
              return "image/jpeg";
            case "png":
              return "image/png";
            case "gif":
              return "image/gif";
            case "webp":
              return "image/webp";
            default:
              return "image/jpeg";
          }
        };

        const contentType = getContentType(selectedImageAsset.uri);
        const fileExtension = contentType === "image/png" ? "png" : "jpg";
        const fileName = `post_${Date.now()}.${fileExtension}`;

        // Get upload URL
        const uploadUrlResponse = await new Promise<{ cdnUrl: string; uploadUrl: string }>(
          (resolve, reject) => {
            getUploadUrl.mutate(
              { contentType, fileName },
              {
                onSuccess: resolve,
                onError: reject,
              }
            );
          }
        );

        // Upload the image
        const response = await fetch(selectedImageAsset.uri);
        const blob = await response.blob();

        const uploadResponse = await fetch(uploadUrlResponse.uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": contentType,
          },
          body: blob,
        });

        if (!uploadResponse.ok) {
          throw new Error("Failed to upload image");
        }

        attachment = {
          type: "image",
          url: uploadUrlResponse.cdnUrl,
          fileName,
        };
      }

      // Create the post
      await new Promise<void>((resolve, reject) => {
        postMutation.mutate(
          { text: postText, attachment },
          {
            onSuccess: () => resolve(),
            onError: reject,
          }
        );
      });

      // Reset form and close modal
      setPostText("");
      setSelectedImage(null);
      setSelectedImageAsset(null);
      setCreatePostModalVisible(false);
      showToastMessage("Post created successfully!");
    } catch (error) {
      console.error("Error creating post:", error);
      Alert.alert("Error", "Failed to create post. Please try again.");
    }
  };

  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };

  const getActorName = (actor: any): string => {
    if (typeof actor === "string") {
      // Extract a readable name from strings like "SU:something:else"
      const parts = actor.split(":");
      return parts[parts.length - 1] || actor;
    }
    if (actor?.data?.name) return actor.data.name;
    if (actor?.data?.firstName && actor?.data?.lastName) {
      return `${actor.data.firstName} ${actor.data.lastName}`;
    }
    if (actor?.id) return `User ${actor.id.substring(0, 8)}`;
    return "Unknown User";
  };

  const getActorImage = (actor: any): string | null => {
    if (actor?.data?.image) return actor.data.image;
    if (actor?.data?.avatarUrl) return actor.data.avatarUrl;
    return null;
  };

  const getActivityContent = (activity: EnrichedActivityWithText): string => {
    if (activity.message) return activity.message;
    if (activity.text) return activity.text;
    if (typeof activity.object === "string") return activity.object;
    const obj = activity.object as any;
    if (obj?.text) return obj.text;
    if (obj?.content) return obj.content;
    return "";
  };

  const isCreator = (actor: any): boolean => {
    if (actor?.data?.role) {
      return actor.data.role === "creator" || actor.data.role === "moderator";
    }
    return false;
  };

  const renderEventCard = (activity: EnrichedActivityWithText) => {
    const eventData = (activity as any).eventData;
    if (!eventData) return null;

    return (
      <View style={styles.eventCard}>
        <View style={styles.eventTimeContainer}>
          <Text style={styles.eventTime}>{eventData.time}</Text>
          <Text style={styles.eventDate}>{eventData.date}</Text>
        </View>
        <View style={styles.eventDetails}>
          <Text style={styles.eventTitle}>{eventData.title}</Text>
          <Text style={styles.eventHost}>By {eventData.host}</Text>
          <View style={styles.eventParticipants}>
            {/* Placeholder for participant avatars */}
          </View>
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#EF5252" />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>
          {error?.message || "An error occurred"}
        </Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handlePostPress = (activity: EnrichedActivityWithText) => {
    router.push({
      pathname: "/post/[id]",
      params: {
        id: activity.id,
        moduleConfig: JSON.stringify(module.config),
      },
    });
  };

  const renderActivity = ({
    item: activity,
  }: {
    item: EnrichedActivityWithText;
  }) => (
    <TouchableOpacity
      style={styles.postCard}
      onPress={() => handlePostPress(activity)}
      activeOpacity={0.8}
    >
      <View style={styles.postHeader}>
        <View style={styles.userInfo}>
          {getActorImage(activity.actor) ? (
            <Image
              source={{ uri: getActorImage(activity.actor)! }}
              style={styles.avatar}
            />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarText}>
                {getActorName(activity.actor)[0]?.toUpperCase() || "U"}
              </Text>
            </View>
          )}
          <View style={styles.userMeta}>
            <View style={styles.nameContainer}>
              <Text style={styles.userName}>
                {getActorName(activity.actor)}
              </Text>
              {isCreator(activity.actor) && (
                <View style={styles.creatorBadge}>
                  <Text style={styles.creatorText}>Creator</Text>
                </View>
              )}
            </View>
            <Text style={styles.timestamp}>{formatTime(activity.time)}</Text>
          </View>
        </View>
        <View style={styles.postHeaderActions}>
          {activity.isPinned && (
            <Pin size={20} color="#FACC15" fill="#FACC15" />
          )}
          <TouchableOpacity
            onPress={(e) => {
              e.stopPropagation();
              handleShare(activity);
            }}
            style={styles.shareButton}
          >
            <Share2 size={16} color="#9A9A9A" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.postContent}>
        <Text style={styles.postText}>{getActivityContent(activity)}</Text>
        
        {/* Display legacy image field */}
        {activity.image && (
          <Image source={{ uri: activity.image }} style={styles.postImage} />
        )}
        
        {/* Display attachments */}
        {activity.attachments && activity.attachments.length > 0 && (
          <View style={styles.attachmentsContainer}>
            {activity.attachments.map((attachment, index) => (
              <View key={index} style={styles.attachmentItem}>
                {attachment.type === "image" && attachment.image_url && (
                  <Image 
                    source={{ uri: attachment.image_url }} 
                    style={styles.postImage} 
                  />
                )}
                {attachment.type === "file" && attachment.asset_url && (
                  <TouchableOpacity 
                    style={styles.fileAttachment}
                    onPress={() => {
                      // Handle file opening - you might want to use a web view or external app
                      Alert.alert("File", "File attachment detected");
                    }}
                  >
                    <Text style={styles.fileAttachmentText}>📎 View File</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>
        )}
        
        {(activity as any).eventData && renderEventCard(activity)}
      </View>

      <View style={styles.postActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={(e) => {
            e.stopPropagation();
            handleLike(activity.id);
          }}
        >
          <Heart
            size={20}
            color={activity.own_reactions?.like?.length ? "#EF5252" : "#fff"}
            fill={
              activity.own_reactions?.like?.length ? "#EF5252" : "transparent"
            }
          />
          <Text
            style={[
              styles.actionText,
              activity.own_reactions?.like?.length
                ? styles.likedText
                : undefined,
            ]}
          >
            {activity.reaction_counts?.like || 0}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={(e) => {
            e.stopPropagation();
            handleComment(activity);
          }}
        >
          <MessageCircle size={20} color="#fff" />
          <Text style={styles.actionText}>
            {activity.reaction_counts?.comment || 0}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderFooter = () => {
    if (isFetchingNextPage) {
      return (
        <View style={styles.loadingMore}>
          <ActivityIndicator size="small" color="#EF5252" />
          <Text style={styles.loadingMoreText}>Loading more...</Text>
        </View>
      );
    }

    if (!hasNextPage && activities.length > 0) {
      return (
        <View style={styles.endOfFeed}>
          <Text style={styles.endOfFeedText}>
            You've reached the end of the feed
          </Text>
        </View>
      );
    }

    return null;
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>No posts yet. Check back later!</Text>
    </View>
  );

  console.log("Rendering activities:", activities.length);

  return (
    <View style={styles.container}>
      {/* Create Post Button */}
      {canPost && (
        <TouchableOpacity 
          style={styles.createPostButton}
          onPress={() => setCreatePostModalVisible(true)}
        >
          <Plus size={20} color="#fff" />
          <Text style={styles.createPostButtonText}>Create Post</Text>
        </TouchableOpacity>
      )}
      
      <FlatList
        data={activities}
        renderItem={renderActivity}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefetching}
            onRefresh={refetch}
            tintColor="#EF5252"
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        contentContainerStyle={styles.listContent}
      />

      {/* Comment Modal */}
      <Modal
        visible={commentModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setCommentModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.commentModal}>
            <View style={styles.commentHeader}>
              <Text style={styles.commentTitle}>Add a comment</Text>
              <TouchableOpacity
                onPress={() => setCommentModalVisible(false)}
                style={styles.closeButton}
              >
                <X size={20} color="#fff" />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.commentInput}
              placeholder="Write your comment..."
              placeholderTextColor="#9A9A9A"
              value={commentText}
              onChangeText={setCommentText}
              multiline
              autoFocus
            />

            <View style={styles.commentActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setCommentModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.submitButton,
                  !commentText.trim() && styles.submitButtonDisabled,
                ]}
                onPress={handleSubmitComment}
                disabled={!commentText.trim()}
              >
                <Send size={16} color="#fff" />
                <Text style={styles.submitButtonText}>Post</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Create Post Modal */}
      <Modal
        visible={createPostModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setCreatePostModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.createPostModal}>
            <View style={styles.createPostHeader}>
              <Text style={styles.createPostTitle}>Create Post</Text>
              <TouchableOpacity
                onPress={() => {
                  setCreatePostModalVisible(false);
                  setPostText("");
                  setSelectedImage(null);
                  setSelectedImageAsset(null);
                }}
                style={styles.closeButton}
              >
                <X size={20} color="#fff" />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.createPostInput}
              placeholder="What's on your mind?"
              placeholderTextColor="#9A9A9A"
              value={postText}
              onChangeText={setPostText}
              multiline
              autoFocus
            />

            {selectedImage && (
              <View style={styles.selectedImageContainer}>
                <Image source={{ uri: selectedImage }} style={styles.selectedImage} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={removeSelectedImage}
                >
                  <X size={20} color="#fff" />
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.createPostActions}>
              <TouchableOpacity
                style={styles.imagePickerButton}
                onPress={handleImagePicker}
              >
                <ImageIcon size={20} color="#9A9A9A" />
                <Text style={styles.imagePickerButtonText}>Add Image</Text>
              </TouchableOpacity>
              
              <View style={styles.createPostButtons}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => {
                    setCreatePostModalVisible(false);
                    setPostText("");
                    setSelectedImage(null);
                    setSelectedImageAsset(null);
                  }}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.submitButton,
                    (!postText.trim() || postMutation.isPending) && styles.submitButtonDisabled,
                  ]}
                  onPress={handleCreatePost}
                  disabled={!postText.trim() || postMutation.isPending}
                >
                  {postMutation.isPending ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Send size={16} color="#fff" />
                  )}
                  <Text style={styles.submitButtonText}>
                    {postMutation.isPending ? "Posting..." : "Post"}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Toast Notification */}
      {showToast && (
        <View style={styles.toast}>
          <Text style={styles.toastText}>{toastMessage}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    backgroundColor: "#000",
    paddingTop: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyContainer: {
    paddingTop: 100,
    alignItems: "center",
  },
  emptyText: {
    color: "#9A9A9A",
    fontSize: 16,
  },
  errorText: {
    color: "#EF5252",
    fontSize: 16,
    marginBottom: 16,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: "#EF5252",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  postCard: {
    backgroundColor: "#171D23",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  postHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: 19,
  },
  avatarPlaceholder: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: "#EF5252",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  userMeta: {
    gap: 2,
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  userName: {
    fontSize: 14,
    fontWeight: "700",
    color: "#fff",
  },
  creatorBadge: {
    backgroundColor: "#EF5252",
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  creatorText: {
    fontSize: 10,
    fontWeight: "500",
    color: "#fff",
  },
  timestamp: {
    fontSize: 12,
    color: "#9A9A9A",
  },
  postContent: {
    marginBottom: 16,
  },
  postText: {
    fontSize: 14,
    color: "#D9D9D9",
    lineHeight: 20,
  },
  postImage: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    marginTop: 12,
  },
  attachmentsContainer: {
    marginTop: 12,
  },
  attachmentItem: {
    marginBottom: 8,
  },
  fileAttachment: {
    backgroundColor: "#333",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  fileAttachmentText: {
    color: "#9A9A9A",
    fontSize: 14,
  },
  createPostButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#EF5252",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 16,
    justifyContent: "center",
    gap: 8,
  },
  createPostButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  createPostModal: {
    backgroundColor: "#171D23",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
    maxHeight: "80%",
  },
  createPostHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  createPostTitle: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  createPostInput: {
    backgroundColor: "#1A1A1A",
    borderRadius: 8,
    padding: 12,
    color: "#fff",
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
    marginBottom: 16,
  },
  selectedImageContainer: {
    position: "relative",
    marginBottom: 16,
  },
  selectedImage: {
    width: "100%",
    height: 200,
    borderRadius: 8,
  },
  removeImageButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    borderRadius: 16,
    padding: 4,
  },
  createPostActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  imagePickerButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    padding: 8,
  },
  imagePickerButtonText: {
    color: "#9A9A9A",
    fontSize: 14,
  },
  createPostButtons: {
    flexDirection: "row",
    gap: 12,
  },
  eventCard: {
    backgroundColor: "#5B6BDB",
    borderRadius: 8,
    padding: 16,
    marginTop: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  eventTimeContainer: {
    alignItems: "flex-end",
  },
  eventTime: {
    color: "#fff",
    fontSize: 14,
    opacity: 0.8,
  },
  eventDate: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "600",
  },
  eventDetails: {
    flex: 1,
  },
  eventTitle: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  eventHost: {
    color: "#fff",
    fontSize: 12,
    opacity: 0.8,
    marginBottom: 8,
  },
  eventParticipants: {
    flexDirection: "row",
  },
  postActions: {
    flexDirection: "row",
    gap: 20,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  actionText: {
    fontSize: 14,
    fontWeight: "700",
    color: "rgba(255, 255, 255, 0.8)",
  },
  likedText: {
    color: "#EF5252",
  },
  listContent: {
    paddingTop: 0,
  },
  postHeaderActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  shareButton: {
    padding: 4,
  },
  loadingMore: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    gap: 8,
  },
  loadingMoreText: {
    color: "#9A9A9A",
    fontSize: 14,
  },
  endOfFeed: {
    alignItems: "center",
  },
  endOfFeedText: {
    color: "#666",
    fontSize: 14,
    textAlign: "center",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    justifyContent: "flex-end",
  },
  commentModal: {
    backgroundColor: "#171D23",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
    maxHeight: "80%",
  },
  commentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  commentTitle: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 4,
  },
  commentInput: {
    backgroundColor: "#1A1A1A",
    borderRadius: 8,
    padding: 12,
    color: "#fff",
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: "top",
    marginBottom: 16,
  },
  commentActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  cancelButtonText: {
    color: "#9A9A9A",
    fontSize: 14,
    fontWeight: "600",
  },
  submitButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#EF5252",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 6,
  },
  submitButtonDisabled: {
    backgroundColor: "#666",
    opacity: 0.5,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  toast: {
    position: "absolute",
    top: 50,
    left: 16,
    right: 16,
    backgroundColor: "#171D23",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#333",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  toastText: {
    color: "#fff",
    fontSize: 14,
    textAlign: "center",
  },
});
